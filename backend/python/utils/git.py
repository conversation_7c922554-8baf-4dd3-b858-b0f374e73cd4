import os
import subprocess
import uuid
import time
from pathlib import Path
from dataclasses import dataclass
from functools import lru_cache
from typing import Dict, Tuple
from utils.logger import logger


@dataclass
class ProjectInfo:
    """项目信息"""
    repo_url: str
    username: str
    branch: str
    source: str


# 项目信息缓存
_project_info_cache: Dict[str, Tuple[ProjectInfo, float]] = {}


def _get_git_dir_mtime(project_path: str) -> float:
    """
    获取 Git 目录的最后修改时间

    Args:
        project_path: 项目路径

    Returns:
        float: 最后修改时间戳，如果没有 Git 目录则返回 0
    """
    try:
        git_dir = Path(project_path) / '.git'
        if git_dir.exists():
            # 检查 .git/HEAD 文件的修改时间（分支切换会更新这个文件）
            head_file = git_dir / 'HEAD'
            if head_file.exists():
                return head_file.stat().st_mtime
            else:
                return git_dir.stat().st_mtime
        return 0
    except (OSError, PermissionError):
        return 0


def _should_refresh_cache(project_path: str, cached_time: float) -> bool:
    """
    判断是否应该刷新缓存

    Args:
        project_path: 项目路径
        cached_time: 缓存时间

    Returns:
        bool: 是否需要刷新缓存
    """
    # 检查 Git 目录修改时间
    git_mtime = _get_git_dir_mtime(project_path)

    # 如果 Git 目录修改时间比缓存时间新，则需要刷新
    if git_mtime > cached_time:
        return True

    # 检查缓存是否过期（默认 5 分钟）
    current_time = time.time()
    cache_ttl = 300  # 5 分钟

    if current_time - cached_time > cache_ttl:
        return True

    return False


@lru_cache(maxsize=32)
def _get_git_branch_cached(project_path: str, git_mtime: float) -> str:
    """
    获取Git分支信息（带缓存）

    Args:
        project_path: 项目路径
        git_mtime: Git目录修改时间（用于缓存失效）

    Returns:
        str: Git分支名称
    """
    resolved_project_path = Path(project_path).resolve()
    branch = 'main'

    try:
        # 使用 subprocess 执行 git 命令
        result = subprocess.run(
            ['git', 'rev-parse', '--abbrev-ref', 'HEAD'],
            cwd=resolved_project_path,
            capture_output=True,
            text=True,
            check=True
        )
        branch = result.stdout.strip()
        logger.debug(f"获取Git分支: {branch} (路径: {project_path})")
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning('[ProjectInfoUtils] Git信息不可用')

    return branch


async def get_git_branch(project_path: str) -> str:
    """
    获取Git分支信息

    Args:
        project_path: 项目路径

    Returns:
        str: Git分支名称
    """
    # 获取Git目录修改时间用于缓存
    git_mtime = _get_git_dir_mtime(project_path)

    # 使用缓存函数
    return _get_git_branch_cached(project_path, git_mtime)


@lru_cache(maxsize=32)
def _get_username_cached(project_path: str, git_mtime: float) -> str:
    """
    获取系统用户名或GIT用户名（带缓存）

    Args:
        project_path: 项目路径
        git_mtime: Git目录修改时间（用于缓存失效）

    Returns:
        str: 用户名
    """
    resolved_project_path = Path(project_path).resolve()
    username = ''

    try:
        # 首先尝试获取 git 用户名
        result = subprocess.run(
            ['git', 'config', 'user.name'],
            cwd=resolved_project_path,
            capture_output=True,
            text=True,
            check=True
        )
        username = result.stdout.strip()
        logger.debug(f"获取Git用户名: {username} (路径: {project_path})")
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning('[ProjectInfoUtils] Git用户名不可用')
        try:
            # 如果 git 用户名获取失败，尝试获取系统用户名
            username = os.getlogin()
            logger.debug(f"获取系统用户名: {username}")
        except OSError:
            logger.warning('[ProjectInfoUtils] 系统用户名不可用')
            # 最后尝试从环境变量获取
            username = os.environ.get('USER', os.environ.get('USERNAME', 'unknown'))
            logger.debug(f"从环境变量获取用户名: {username}")

    return username


async def get_username(project_path: str) -> str:
    """
    获取系统用户名或GIT用户名

    Args:
        project_path: 项目路径

    Returns:
        str: 用户名
    """
    # 获取Git目录修改时间用于缓存
    git_mtime = _get_git_dir_mtime(project_path)

    # 使用缓存函数
    return _get_username_cached(project_path, git_mtime)


@lru_cache(maxsize=1)
def get_mac_address() -> str:
    """
    获取MAC地址（带缓存）

    Returns:
        str: MAC地址
    """
    return '00:00:00:00:00:00'


def construct_repo_url(mac_address: str, relative_path: str) -> str:
    """
    构造repo_url（包括路径截断处理）

    Args:
        mac_address: MAC地址
        relative_path: 相对于主目录的路径

    Returns:
        str: 构造的repo_url
    """
    # 组合MAC地址和相对路径，例如 00:00:00:00:00:00/d/work/project
    repo_url = f"{mac_address}{relative_path}"

    # 控制repo_url在80字符以内，如果路径太长则省略中间部分
    if len(repo_url) > 80:
        max_length = 80
        mac_length = len(mac_address)
        available_length = max_length - mac_length

        if available_length > 0:
            # 如果可用长度大于等于 "..."+最后部分的长度，则进行截断
            if available_length >= 3 + min(10, len(relative_path)):
                start_length = (available_length - 3) // 2
                end_length = available_length - 3 - start_length

                if end_length < len(relative_path):
                    start = relative_path[:start_length]
                    end = relative_path[-end_length:] if end_length > 0 else ''
                    relative_path = f"{start}...{end}"
            else:
                # 如果可用长度太小，只保留最后的部分
                relative_path = relative_path[-available_length:] if available_length > 0 else ''

            repo_url = f"{mac_address}{relative_path}"
        else:
            # 如果MAC地址就已经超过80字符，只保留MAC地址
            repo_url = mac_address[:80]

    return repo_url


async def get_project_info(project_path: str, force_refresh: bool = False) -> ProjectInfo:
    """
    获取项目信息，包括MAC地址、用户名、Git分支和repo_url

    Args:
        project_path: 项目路径
        force_refresh: 是否强制刷新缓存

    Returns:
        ProjectInfo: 包含项目信息的对象
    """
    global _project_info_cache

    # 标准化路径作为缓存键
    cache_key = str(Path(project_path).resolve())
    current_time = time.time()

    # 检查缓存
    if not force_refresh and cache_key in _project_info_cache:
        cached_info, cached_time = _project_info_cache[cache_key]

        # 判断是否需要刷新缓存
        if not _should_refresh_cache(project_path, cached_time):
            logger.debug(f"使用缓存的项目信息: {cache_key}")
            return cached_info
        else:
            logger.debug(f"缓存已过期，重新获取项目信息: {cache_key}")

    # 获取新的项目信息
    logger.debug(f"获取项目信息: {cache_key}")

    # 获取git分支信息
    branch = await get_git_branch(project_path)

    # 获取系统用户名 or GIT用户名
    username = await get_username(project_path)

    # 获取MAC地址
    mac_address = get_mac_address()

    # 构造repo_url
    repo_url = construct_repo_url(mac_address, project_path)

    project_info = ProjectInfo(
        branch=branch,
        username=username,
        repo_url=repo_url,
        source='local'
    )

    # 更新缓存
    _project_info_cache[cache_key] = (project_info, current_time)

    # 清理过期缓存（保持缓存大小合理）
    _cleanup_expired_cache()

    return project_info


def _cleanup_expired_cache():
    """清理过期的缓存条目"""
    global _project_info_cache

    current_time = time.time()
    cache_ttl = 300  # 5 分钟

    # 找出过期的缓存键
    expired_keys = []
    for cache_key, (_, cached_time) in _project_info_cache.items():
        if current_time - cached_time > cache_ttl:
            expired_keys.append(cache_key)

    # 删除过期缓存
    for key in expired_keys:
        del _project_info_cache[key]

    if expired_keys:
        logger.debug(f"清理了 {len(expired_keys)} 个过期的项目信息缓存")


def clear_project_info_cache(project_path: str = None):
    """
    清理项目信息缓存

    Args:
        project_path: 项目路径，如果为 None 则清理所有缓存
    """
    global _project_info_cache

    if project_path is None:
        # 清理所有缓存
        cache_count = len(_project_info_cache)
        _project_info_cache.clear()
        logger.info(f"清理了所有项目信息缓存 ({cache_count} 个)")
    else:
        # 清理特定项目的缓存
        cache_key = str(Path(project_path).resolve())
        if cache_key in _project_info_cache:
            del _project_info_cache[cache_key]
            logger.info(f"清理了项目缓存: {cache_key}")


def get_project_info_cache_stats() -> Dict[str, int]:
    """
    获取项目信息缓存统计

    Returns:
        Dict[str, int]: 缓存统计信息
    """
    global _project_info_cache

    current_time = time.time()
    cache_ttl = 300  # 5 分钟

    total_count = len(_project_info_cache)
    expired_count = 0

    for _, (_, cached_time) in _project_info_cache.items():
        if current_time - cached_time > cache_ttl:
            expired_count += 1

    return {
        'total': total_count,
        'valid': total_count - expired_count,
        'expired': expired_count
    }


def clear_all_git_caches():
    """
    清理所有Git相关的缓存
    """
    # 清理项目信息缓存
    clear_project_info_cache()

    # 清理LRU缓存
    _get_git_branch_cached.cache_clear()
    _get_username_cached.cache_clear()
    get_mac_address.cache_clear()

    logger.info("已清理所有Git相关缓存")


def get_all_cache_stats() -> Dict[str, any]:
    """
    获取所有缓存的统计信息

    Returns:
        Dict[str, any]: 所有缓存的统计信息
    """
    project_info_stats = get_project_info_cache_stats()

    return {
        'project_info': project_info_stats,
        'git_branch': {
            'hits': _get_git_branch_cached.cache_info().hits,
            'misses': _get_git_branch_cached.cache_info().misses,
            'current_size': _get_git_branch_cached.cache_info().currsize,
            'max_size': _get_git_branch_cached.cache_info().maxsize
        },
        'username': {
            'hits': _get_username_cached.cache_info().hits,
            'misses': _get_username_cached.cache_info().misses,
            'current_size': _get_username_cached.cache_info().currsize,
            'max_size': _get_username_cached.cache_info().maxsize
        },
        'mac_address': {
            'hits': get_mac_address.cache_info().hits,
            'misses': get_mac_address.cache_info().misses,
            'current_size': get_mac_address.cache_info().currsize,
            'max_size': get_mac_address.cache_info().maxsize
        }
    }