from modules.integrations.tools.search.abc_search import SearchToolABC


class CompleteSearchTool(SearchToolABC):
    def __init__(self, repo_path, **args):
        pass

    @property
    def description(self):
        return """- `complete`: Signal that the search can be completed with sufficient context information.

  This tool is used to indicate that the current context information from files is sufficient
  to answer the user's query, and the search process can be terminated.

  Parameters:
  - `file_paths` (required): Relative file paths separated by commas. Must be known file paths.
    For paths from directory context, you need to concatenate the directory with the file paths.
  - `scores` (required): Relevance scores for the files separated by commas, indicating how
    relevant each file is to answering the user's query.

  Use cases:
  - When gathered file information is sufficient to answer the user's query
  - To prevent unnecessary continued searching when enough context is available
  - To optimize search efficiency by stopping at the right time"""

    @property
    def examples(self):
        return """<output>
    <complete>
    <file_paths>src/auth/login.py,src/auth/validation.py</file_paths>
    <scores>0.95,0.87</scores>
    </complete>

    <complete>
    <file_paths>backend/database/connection.py,backend/models/user.py,backend/utils/helpers.py</file_paths>
    <scores>0.92,0.88,0.75</scores>
    </complete>
</output>"""

# 标识搜索可以结束的工具

# Usage:
# - 当已有的上下文信息对应的文件信息已经足够回答用户的问题时，调用本工具结束搜索

# - Parameters:
# - file_paths: 文件的相对路径，多个文件之间使用 , 进行分隔，文件路径必须是已知的文件路径，从目录上下文信息中得到的路径需要你拼接目录与目录下的文件路径
# - score：文件的相关性评分，多个评分之间使用 , 进行分隔
