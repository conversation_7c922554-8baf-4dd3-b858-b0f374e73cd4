import asyncio
import hashlib
import os
from pathlib import Path
from typing import List, Optional
import xml.etree.ElementTree as ET

from modules.integrations.tools.search.abc_search import SearchToolABC
from modules.common.schema import CodeSnippet
from modules.integrations.apis.embedding.embedding_client import EmbeddingClient
from modules.integrations.apis.embedding.schemas import (
    ProjectCreateRequest,
    CompareRequest,
    CompareResponse,
    MerkleNode,
    UploadRequest,
    FileUploadItem,
    QueryRequest,
)
from utils.git import get_project_info
from utils.file import _read_file_lines_cached, FileFilterMode
from utils.trace_logger import get_trace_logger
from utils.convert import extract_text_from_xml_element, safe_parse_xml_with_preprocessing

logger = get_trace_logger(__name__)


class EmbeddingSearchTool(SearchToolABC):
    """基于向量数据库的代码搜索工具"""

    def __init__(self, repo_path: str, refresh: bool = False):
        """
        初始化嵌入搜索工具

        Args:
            repo_path: 仓库路径
            refresh: 是否强制刷新项目
        """
        self.repo_path = repo_path
        self.embedding_client = EmbeddingClient()
        self.project_id: Optional[str] = None
        self._initialization_complete = False
        self._initialization_task = None

        # 同步等待异步初始化完成
        import asyncio
        try:
            # 如果当前已经在事件循环中
            loop = asyncio.get_running_loop()
            # 创建初始化任务，但不在这里等待（避免阻塞事件循环）
            self._initialization_task = loop.create_task(self._initialize_project(refresh))
            # 添加完成回调
            self._initialization_task.add_done_callback(self._on_initialization_complete)
        except RuntimeError:
            # 没有运行中的事件循环，可以直接运行
            asyncio.run(self._initialize_project(refresh))
            self._initialization_complete = True

    def _on_initialization_complete(self, task):
        """初始化完成回调"""
        try:
            task.result()  # 获取结果，如果有异常会抛出
            self._initialization_complete = True
            logger.info("项目初始化完成")
        except Exception as e:
            logger.error(f"项目初始化失败: {e}")
            self._initialization_complete = False

    async def _ensure_initialized(self):
        """确保项目已初始化"""
        if self._initialization_complete:
            return

        if self._initialization_task and not self._initialization_task.done():
            # 等待初始化任务完成
            await self._initialization_task
        elif not self._initialization_complete:
            # 如果没有初始化任务或任务已完成但初始化失败，抛出异常
            raise RuntimeError("项目未正确初始化，请检查配置和网络连接")

    async def _initialize_project(self, refresh: bool = True):
        """
        初始化项目

        Args:
            refresh: 是否强制刷新项目
        """
        try:
            # 获取项目信息（使用缓存）
            project_info = await get_project_info(self.repo_path, force_refresh=refresh)

            logger.info(f"项目信息: 分支={project_info.branch}, 用户={project_info.username}")

            # 创建项目
            create_request = ProjectCreateRequest(
                username=project_info.username,
                source=project_info.source,
                branch=project_info.branch,
                repo_url=project_info.repo_url
            )

            create_response = await self.embedding_client.create_project(create_request)
            self.project_id = create_response.project_id

            logger.info(f"项目初始化成功: {self.project_id}, 是否新项目: {create_response.is_new}")

            # 如果是新项目或者需要刷新，则上传项目
            if create_response.is_new or refresh:
                # 检查是否需要更新
                await self._check_and_update_project()

        except Exception as e:
            logger.error(f"项目初始化失败: {e}")
            raise

    async def _check_and_update_project(self):
        """检查并更新项目"""
        try:
            # 构建本地 Merkle 树
            merkle_tree = self._build_merkle_tree()

            # 与服务器比较
            compare_request = CompareRequest(merkle_tree=merkle_tree)
            compare_response = await self.embedding_client.compare_with_server(
                self.project_id, compare_request
            )

            # 如果有差异，则上传更新的文件
            if (compare_response.diff_files.added or
                compare_response.diff_files.modified or
                compare_response.diff_files.deleted):

                logger.info(f"检测到文件变更: 新增{len(compare_response.diff_files.added)}, "
                          f"修改{len(compare_response.diff_files.modified)}, "
                          f"删除{len(compare_response.diff_files.deleted)}")

                await self._upload_changed_files(compare_response)
            else:
                logger.info("项目文件无变更")

        except Exception as e:
            logger.error(f"检查项目更新失败: {e}")

    def _build_merkle_tree(self) -> MerkleNode:
        """构建 Merkle 树"""
        def build_node(path: Path, relative_path: str) -> MerkleNode:
            if path.is_file():
                # 计算文件哈希
                try:
                    cached_content = self._read_file_content_cached(relative_path)
                    file_hash = hashlib.sha256(cached_content.encode('utf-8')).hexdigest()
                except Exception as e:
                    logger.warning(f"计算文件哈希失败 {path}: {e}")
                    file_hash = hashlib.sha256(str(path).encode()).hexdigest()
                return MerkleNode(
                    path=os.path.basename(relative_path),
                    hash=file_hash,
                    is_file=True,
                    children=[]
                )
            else:
                # 目录节点
                children = []
                try:
                    for child_path in sorted(path.iterdir()):
                        child_relative = os.path.join(relative_path, child_path.name)

                        # 应用文件过滤
                        from utils.file import should_ignore_path
                        if not should_ignore_path(child_path, FileFilterMode.EMBEDDING):
                            child_node = build_node(child_path, child_relative)
                            children.append(child_node)
                except PermissionError:
                    pass

                # 计算目录哈希（基于子节点哈希）
                child_hashes = [child.hash for child in children]
                dir_hash = hashlib.sha256(''.join(sorted(child_hashes)).encode()).hexdigest()

                return MerkleNode(
                    path=os.path.basename(relative_path),
                    hash=dir_hash,
                    is_file=False,
                    children=children
                )

        repo_path = Path(self.repo_path)
        return build_node(repo_path, ".")


    def _read_file_content_cached(self, file_path: str) -> str:
        """
        读取文件内容（带缓存）

        Args:
            file_path: 相对于仓库根目录的文件路径

        Returns:
            str: 文件内容
        """
        try:
            full_path = os.path.join(self.repo_path, file_path)

            # 获取文件修改时间用于缓存
            file_mtime = os.path.getmtime(full_path)

            # 获取缓存的文件行
            cached_lines = _read_file_lines_cached(full_path, file_mtime)

            if cached_lines:
                content = ''.join(cached_lines)
                logger.debug(f"从缓存读取文件内容: {file_path} ({len(content)} 字符)")
                return content
            else:
                logger.warning(f"文件内容为空: {file_path}")
                return ""

        except OSError as e:
            logger.warning(f"读取文件失败 {file_path}: {e}")
            return ""

    async def _upload_changed_files(self, compare_response: CompareResponse):
        """上传变更的文件"""
        try:
            # 获取需要上传的文件列表
            files_to_upload = (
                compare_response.diff_files.added +
                compare_response.diff_files.modified
            )

            if not files_to_upload:
                logger.info("没有需要上传的文件")
                return

            # 读取文件内容
            upload_items = []
            for file_path in files_to_upload:
                try:
                    # 使用带缓存的方法读取文件内容
                    content = self._read_file_content_cached(file_path)

                    # 计算文件哈希
                    file_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()

                    upload_items.append(FileUploadItem(
                        file_path=file_path,
                        file_content=content,
                        file_hash=file_hash
                    ))
                except Exception as e:
                    logger.warning(f"读取文件失败 {file_path}: {e}")
                    continue

            if upload_items:
                # 创建上传请求
                upload_request = UploadRequest(
                    files=upload_items,
                    auto_commit=True
                )

                # 上传文件
                upload_response = await self.embedding_client.upload_files(
                    task_id=compare_response.task_id,
                    request=upload_request
                )

                logger.info(f"增量文件上传完成，任务ID: {upload_response.task_id}")

                # 等待处理完成
                await self._wait_for_task_completion(upload_response.task_id)

        except Exception as e:
            logger.error(f"增量文件上传失败: {e}")
            raise

    async def _wait_for_task_completion(self, task_id: str, max_wait_time: int = 300):
        """等待任务完成"""
        import time
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                status_response = await self.embedding_client.get_task_status(task_id)

                if status_response.is_terminal:
                    if status_response.status == 'success':
                        logger.info(f"任务 {task_id} 完成成功")
                        return
                    else:
                        logger.error(f"任务 {task_id} 失败: {status_response.error_message}")
                        raise Exception(f"任务失败: {status_response.error_message}")

                # 显示进度
                if status_response.processing_progress:
                    progress = status_response.processing_progress
                    logger.info(f"任务进度: {progress.progress_percentage:.1f}% "
                              f"({progress.completed_files}/{progress.total_files})")

                # 等待一段时间后再次检查
                await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"检查任务状态失败: {e}")
                await asyncio.sleep(5)

        logger.warning(f"任务 {task_id} 等待超时")

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'query': query.strip(),
            'top_k': 10
        }

        # 尝试解析XML格式
        if query.strip().startswith('<embedding_search>'):
            try:
                # 解析XML
                root = safe_parse_xml_with_preprocessing(query.strip(), 'query')

                # 提取查询内容
                query_elem = root.find('query')
                if query_elem is not None and query_elem.text:
                    query_content = extract_text_from_xml_element(query_elem)
                    params['query'] = query_content.strip()

                # 提取top_k参数
                top_k_elem = root.find('top_k')
                if top_k_elem is not None and top_k_elem.text:
                    try:
                        params['top_k'] = int(top_k_elem.text.strip())
                    except ValueError:
                        logger.warning(f"Invalid top_k value: {top_k_elem.text}, using default 10")

                logger.debug(f"Successfully parsed XML query: query={params['query']}, top_k={params['top_k']}")

            except ET.ParseError as e:
                logger.warning(f"Failed to parse XML query, using as plain text: {e}")
                logger.debug(f"Failed XML content: {query.strip()}")

        return params

    async def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        搜索代码

        Args:
            query: 搜索查询（支持XML格式和普通文本）
            **kwargs: 其他搜索参数

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        # 确保项目已初始化
        await self._ensure_initialized()

        if not self.project_id:
            logger.error("项目未初始化，无法进行搜索")
            return []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 构建查询请求
            query_request = QueryRequest(
                query=search_params['query'],
                project_id=self.project_id,
                retrieval_type=kwargs.get('retrieval_type', 'chunk'),
                top_k=search_params['top_k'],
                score_threshold=kwargs.get('score_threshold', 0.5),
                rewrite=kwargs.get('rewrite', False)
            )

            # 执行查询
            query_response = await self.embedding_client.query_codebase(query_request)

            # 转换结果为 CodeSnippet
            code_snippets = []
            
            for doc in query_response.documents:
                # 构建代码片段路径

                code_snippet = CodeSnippet(
                    file_path=doc.metadata.file_path,
                    content="",  # 内容需要从本地文件读取
                    score=doc.score,
                    start_line=doc.metadata.location[0],
                    end_line=doc.metadata.location[1]
                )
                code_snippets.append(code_snippet)

            # 读取代码片段内容
            self._fill_snippet_content(code_snippets)

            return code_snippets

        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []

    async def search_async(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        异步搜索代码（与search方法功能相同，为了接口一致性而添加）

        Args:
            query: 搜索查询（支持XML格式和普通文本）
            **kwargs: 其他搜索参数

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        # 直接调用search方法，因为search方法本身就是异步的
        return await self.search(query, **kwargs)

    def _fill_snippet_content(self, code_snippets: List[CodeSnippet]):
        """填充代码片段内容"""
        from utils.file import read_chunk_contents

        # 构建 chunk_paths 列表
        chunk_path2code_snippet = {f"{snippet.file_path}:{snippet.start_line}-{snippet.end_line}": snippet
                           for snippet in code_snippets}

        # 批量读取内容
        chunk_contents = read_chunk_contents(self.repo_path, list(chunk_path2code_snippet.keys()))

        # 填充内容
        for chunk_path, snippet in chunk_path2code_snippet.items():
            if chunk_path in chunk_contents:
                snippet.content = chunk_contents[chunk_path]

    @property
    def description(self):
        """搜索工具描述"""
        return """- `embedding`: Vector-based semantic code search using embeddings.

  This tool provides semantic search capabilities by converting code into vector embeddings
  and finding semantically similar code snippets based on the query.

  Parameters:
  - `query` (required): Natural language description of what you're looking for
  - `top_k` (optional): Maximum number of results to return (default: 10)

  Use cases:
  - Find code snippets that implement specific functionality
  - Locate similar code patterns or associated documents
  - Search for code based on natural language descriptions
  - Discover relevant code sections for understanding or modification

  Advantages:
  - Semantic understanding beyond keyword matching
  - Can find conceptually similar code even with different terminology
  - Handles natural language queries effectively
  - Provides relevance scoring for results
"""

    @property
    def examples(self):
        """搜索工具示例"""
        return """<output>
    <embedding>
    <query>user authentication and login validation</query>
    <top_k>5</top_k>
    </embedding>

    <embedding>
    <query>database connection and SQL query execution</query>
    <top_k>10</top_k>
    </embedding>

    <embedding>
    <query>exception handling and error recovery mechanisms</query>
    </embedding>
</output>"""
        
