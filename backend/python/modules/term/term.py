# 分词工具
import re
import os
import json
import math
from rich.progress import track
from enum import Enum
import jieba
from functools import lru_cache
from typing import Dict, List, Set, Iterator, Tuple
from collections import defaultdict

from core.config import get_config
from modules.chunks.IChunk import IChunk
from modules.common.constants import FileFilterMode
from modules.chunks.chunk_factory import getChunkService
from modules.integrations.database.sqlite.client import Repo, get_sqlite_client

from utils.file import build_file_list, FileType
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

# 获取支持 trace 的 logger

from collections import Counter

def normalize(word: str) -> str:
    return word.lower()

IGNORED_TERMS = set()

def should_ignore(term: str) -> bool:
    global IGNORED_TERMS
    
    if not IGNORED_TERMS:
        with open(get_config().data.ignored_terms_path, 'r', encoding='utf-8') as f:
            IGNORED_TERMS = set([line.strip() for line in f.readlines()])
    
    return normalize(term) in IGNORED_TERMS

@lru_cache(maxsize=128)
def get_terms(text: str, file_type: FileType = FileType.DOC) -> List[str]:
    """
    Break a string into terms (words).

    Args:
        text: Input string to tokenize

    Returns:
        List of normalized terms
    """
    if file_type == FileType.CODE:
        return list(_split_code_terms(text))
    elif file_type == FileType.DOC:
        return list(_split_doc_terms(text))
    else:
        raise ValueError(f"Unknown file type: {file_type}")
    
def _split_code_terms(input_text: str) -> Iterator[str]:
    """
    Generator that yields normalized terms from input text.

    Only matches words that are at least 3 characters long and start with a letter.
    Handles camelCase, snake_case, and numeric suffix splitting.
    """
    

    # Unicode regex pattern equivalent to the JavaScript version
    # Matches words that:
    # - Are at least 3 characters long
    # - Start with a letter, underscore, or dollar sign
    # - Contain only letters, numbers, underscores, or dollar signs
    # - Are not preceded or followed by word characters
    pattern = r'(?<![a-zA-Z0-9_$])[a-zA-Z_$][a-zA-Z0-9_$]{2,}(?![a-zA-Z0-9_$])'

    for match in re.finditer(pattern, input_text):
        word = match.group(0)
        if should_ignore(word):
            continue

        parts: Set[str] = set()
        parts.add(normalize(word))

        sub_parts: List[str] = []

        # Handle camelCase splitting - improved to handle consecutive capitals
        # Split on transitions from lowercase/digit to uppercase
        camel_parts = re.split(r'(?<=[a-z0-9])(?=[A-Z])', word)
        if len(camel_parts) > 1:
            sub_parts.extend(camel_parts)

        # Handle snake_case splitting
        snake_parts = word.split('_')
        if len(snake_parts) > 1:
            # Filter out empty parts that might result from leading/trailing underscores
            snake_parts = [part for part in snake_parts if part]
            sub_parts.extend(snake_parts)

            # Also try to split each snake_case part further if it contains camelCase
            for snake_part in snake_parts:
                if snake_part:
                    camel_in_snake = re.split(r'(?<=[a-z0-9])(?=[A-Z])', snake_part)
                    if len(camel_in_snake) > 1:
                        sub_parts.extend(camel_in_snake)

        # Handle numeric suffix (e.g., "variable123" -> "variable")
        non_digit_prefix_match = re.match(r'^([^0-9]+)[0-9]+$', word)
        if non_digit_prefix_match:
            prefix = non_digit_prefix_match.group(1)
            # Remove trailing underscores from prefix
            prefix = prefix.rstrip('_')
            if prefix:
                sub_parts.append(prefix)

        # Process sub parts
        for part in sub_parts:
            # Clean the part
            part = part.strip('_')
            if should_ignore(part):
                continue

            # Accept parts with at least 2 characters if they contain letters
            # This allows shorter meaningful words like "my", "is", "to", etc.
            if len(part) >= 2 and re.search(r'[a-zA-Z]', part):
                # For very short parts (2 chars), require at least 2 letters
                if len(part) == 2 and not re.match(r'^[a-zA-Z]{2}$', part):
                    continue
                parts.add(normalize(part))

        yield from parts


def _split_doc_terms(input_text: str) -> Iterator[str]:
    """
    Generator that yields normalized terms from input text.

    Only matches words that are at least 3 characters long and start with a letter.
    Handles camelCase, snake_case, and numeric suffix splitting.
    """
    
    for word in jieba.cut(input_text, cut_all=True):
        if should_ignore(word):
            continue
        
        yield normalize(word)


def calculate_bm25_chunk_terms(project_dir: str, 
                               chunk_splitter: IChunk = getChunkService(get_config().chunk.name)(),
                               k1: float = 1.2,
                               b: float = 0.75) -> Tuple[Dict, Dict]:
    """
    加载上下文文件并构建BM25索引
    """
    logger.info(f"Building BM25 Index for {project_dir}..., k1: {k1}, b: {b}")
    
    # 读取项目目录中的文件
    doc_contents = build_file_list(root_dir=project_dir, start_dir=project_dir, filter_mode=FileFilterMode.LOCAL)

    # 代码切分
    chunk_content = {}
    chunk_keystructures = {}
    for doc_path, content in track(doc_contents.items(), description="Chunking files..."):
        key_structure, chunks = chunk_splitter.chunk_file(doc_path, content)
        for chunk in chunks:
            chunk_content[f"{doc_path}:{chunk.start_line}-{chunk.end_line}"] = chunk.content
        chunk_keystructures[doc_path] = key_structure

    # 计算所有chunk中的term和IDF值
    all_terms = set()
    chunk_terms = {}
    chunk_freqs = {}
    for chunk_path, content in track(chunk_content.items(), description="Processing files..."):
        terms = get_terms(content, FileType.from_suffix(chunk_path.split(':')[0].split('.')[-1]))
        chunk_terms[chunk_path] = terms
        all_terms.update(terms)
        for term in set(terms):
            chunk_freqs[term] = chunk_freqs.get(term, 0) + 1
    
    # 计算平均chunk长度
    total_chunk_length = sum(len(terms) for terms in chunk_terms.values())
    chunk_doc_length = total_chunk_length * 1.0 / len(chunk_terms) if chunk_terms else 1
    logger.info(f"Average Chunk Length: {chunk_doc_length}")

    # 计算每个term的IDF值
    total_chunks = len(chunk_content)
    term_idf = {}

    for term in track(all_terms, description="Calculating IDF..."):
        chunk_freq = chunk_freqs.get(term, 0)
        # IDF计算：log(N - df/df)，其中N是总文档数，df是包含该term的文档数
        term_idf[term] = math.log((total_chunks * 1.0  - chunk_freq) / chunk_freq) if chunk_freq > 0 else 0

    # 计算每个文件内每个term的频率
    chunks_term_freqs = {}
    for chunk_path, terms in track(chunk_terms.items(), description="Calculating term frequencies..."):
        term_counts = Counter(terms)
        chunk_tf = {}
        for term, term_count in term_counts.items():
            chunk_tf[term] = term_count * (k1 + 1) / (term_count + k1 * (1 - b + b * (len(terms) * 1.0 / chunk_doc_length)))

        chunks_term_freqs[chunk_path] = dict(term_counts)

    # 缓存结果
    cache_data = {
        'chunk_content': chunk_content, # 存下chunk_content是为了可以局部更新数据，而不必每次重新计算整个项目的BM25
        'term_idf': term_idf,
        'chunk_term_freqs': chunks_term_freqs,
        'avg_chunk_length': chunk_doc_length
    }

    return chunk_keystructures, cache_data


def calculate_inverted_index(project_dir: str, chunk_keywords_num: int = 20) -> Dict:
    """
    为每个chunk提取关键词并构建倒排索引
    """
    
    # 加载BM25缓存文件
    client = get_sqlite_client()
    repo: Repo = client.get_repo(project_dir)
    assert repo is not None and repo.term_sparse is not None, f"Repo {project_dir} not found or term sparse not calculated"
    
    term_sparse_data = json.loads(repo.term_sparse)
    
    # 读取BM25缓存文件
    term_idf = None
    chunk_tf = {}

    term_idf = term_sparse_data['term_idf']
    chunk_tf = term_sparse_data['chunk_term_freqs']
    
    # 为每个chunk提取关键词
    keywords2chunk = defaultdict(list)
    chunk_path2idx = []

    chunk_path_idx = 0
    for chunk_path, term_tfs in track(chunk_tf.items(), description="Extracting keywords..."):
        # 计算所有term_freqs的BM25分数
        term_bm25 = {}
        for term, term_tf in term_tfs.items():
            if term_idf.get(term, 0) == 0:
                continue
            term_bm25[term] = term_tf * term_idf[term]
        
        # 保留BM25分数最高的前N个关键词
        sorted_terms = sorted(term_bm25.items(), key=lambda x: x[1], reverse=True)[:min(chunk_keywords_num, len(term_bm25))]
        for term, _ in sorted_terms:
            keywords2chunk[term].append(chunk_path_idx)
        
        # 记录chunk_path到idx的映射 
        chunk_path2idx.append(chunk_path)
        chunk_path_idx += 1

    return {
            'keywords2chunk': keywords2chunk,
            'chunk_path2idx': chunk_path2idx,
        }



if __name__ == "__main__":
    # # Generate Ignored Terms
    # repos_dir = "/Users/<USER>/01-Projects/treo/data/repos"
    # file2content = build_file_list(repos_dir, filter_mode=FileFilterMode.LOCAL)
    
    # import rich

    # term_cnts = defaultdict(int)
    # for file_path, content in rich.progress.track(file2content.items(), description="Counting terms..."):
    #     terms = get_terms(content, FileType.from_suffix(file_path.split('.')[-1]))
    #     for term in terms:
    #         term_cnts[term] += 1

    # # 排序
    # sorted_terms = sorted(term_cnts.items(), key=lambda x: x[1], reverse=True)[:1000]

    # # 保存到文件
    # with open(os.path.join(repos_dir, "terms.json"), 'w', encoding='utf-8') as f:
    #     json.dump(sorted_terms, f, ensure_ascii=False, indent=2)

    res = _split_code_terms("def connnection_pools()")
    print(list(res))
    


